import { useState, useEffect } from 'react'
import { Filter, X, Search } from 'lucide-react'

const FilterPanel = ({ filters, setFilters, onApply, onClear, summary }) => {
  const [availableDistricts, setAvailableDistricts] = useState([])
  const [availableTehsils, setAvailableTehsils] = useState([])

  useEffect(() => {
    if (summary) {
      // Filter districts based on selected state
      if (filters.state) {
        // In a real app, you'd filter districts by state
        setAvailableDistricts(summary.districts)
      } else {
        setAvailableDistricts(summary.districts)
      }
    }
  }, [filters.state, summary])

  useEffect(() => {
    if (summary) {
      // Filter tehsils based on selected district
      if (filters.district) {
        // In a real app, you'd filter tehsils by district
        setAvailableTehsils(summary.tehsils)
      } else {
        setAvailableTehsils(summary.tehsils)
      }
    }
  }, [filters.district, summary])

  const scenarios = [
    { value: 'ssp126', label: 'SSP1-2.6 (Low Emissions)' },
    { value: 'ssp245', label: 'SSP2-4.5 (Medium-Low Emissions)' },
    { value: 'ssp370', label: 'SSP3-7.0 (Medium-High Emissions)' },
    { value: 'ssp585', label: 'SSP5-8.5 (High Emissions)' }
  ]

  const timePeriods = [
    { value: 'historical', label: 'Historical' },
    { value: 'nearTerm', label: 'Near-term (2030s)' },
    { value: 'midTerm', label: 'Mid-term (2050s)' },
    { value: 'longTerm', label: 'Long-term (2080s)' }
  ]

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  if (!summary) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-10 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Filter className="h-5 w-5 text-gray-600 mr-2" />
          <h2 className="text-lg font-semibold text-gray-900">Filters</h2>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={onApply}
            className="btn-primary"
          >
            Apply Filters
          </button>
          <button
            onClick={onClear}
            className="btn-secondary flex items-center"
          >
            <X className="h-4 w-4 mr-1" />
            Clear
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        {/* State Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            State
          </label>
          <select
            value={filters.state}
            onChange={(e) => handleFilterChange('state', e.target.value)}
            className="select-field"
          >
            <option value="">All States</option>
            {summary.states.map(state => (
              <option key={state} value={state}>{state}</option>
            ))}
          </select>
        </div>

        {/* District Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            District
          </label>
          <select
            value={filters.district}
            onChange={(e) => handleFilterChange('district', e.target.value)}
            className="select-field"
            disabled={!filters.state}
          >
            <option value="">All Districts</option>
            {availableDistricts.slice(0, 100).map(district => (
              <option key={district} value={district}>{district}</option>
            ))}
          </select>
        </div>

        {/* Tehsil Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Tehsil
          </label>
          <select
            value={filters.tehsil}
            onChange={(e) => handleFilterChange('tehsil', e.target.value)}
            className="select-field"
            disabled={!filters.district}
          >
            <option value="">All Tehsils</option>
            {availableTehsils.slice(0, 100).map(tehsil => (
              <option key={tehsil} value={tehsil}>{tehsil}</option>
            ))}
          </select>
        </div>

        {/* Scenario Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Climate Scenario
          </label>
          <select
            value={filters.scenario}
            onChange={(e) => handleFilterChange('scenario', e.target.value)}
            className="select-field"
          >
            {scenarios.map(scenario => (
              <option key={scenario.value} value={scenario.value}>
                {scenario.label}
              </option>
            ))}
          </select>
        </div>

        {/* Time Period Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Time Period
          </label>
          <select
            value={filters.timePeriod}
            onChange={(e) => handleFilterChange('timePeriod', e.target.value)}
            className="select-field"
          >
            {timePeriods.map(period => (
              <option key={period.value} value={period.value}>
                {period.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Filter Summary */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="flex flex-wrap gap-2">
          {filters.state && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              State: {filters.state}
              <button
                onClick={() => handleFilterChange('state', '')}
                className="ml-1 text-blue-600 hover:text-blue-800"
              >
                <X className="h-3 w-3" />
              </button>
            </span>
          )}
          {filters.district && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
              District: {filters.district}
              <button
                onClick={() => handleFilterChange('district', '')}
                className="ml-1 text-green-600 hover:text-green-800"
              >
                <X className="h-3 w-3" />
              </button>
            </span>
          )}
          {filters.tehsil && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
              Tehsil: {filters.tehsil}
              <button
                onClick={() => handleFilterChange('tehsil', '')}
                className="ml-1 text-purple-600 hover:text-purple-800"
              >
                <X className="h-3 w-3" />
              </button>
            </span>
          )}
        </div>
      </div>
    </div>
  )
}

export default FilterPanel
