import { useEffect, useRef } from 'react'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'

// Fix for default markers in Leaflet with Vite
delete L.Icon.Default.prototype._getIconUrl
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
})

const ClimateMap = ({ data, onMapClick, selectedLocation }) => {
  const mapRef = useRef(null)
  const mapInstanceRef = useRef(null)
  const markersRef = useRef([])

  useEffect(() => {
    if (!mapRef.current) return

    // Initialize map
    mapInstanceRef.current = L.map(mapRef.current).setView([20.5937, 78.9629], 5)

    // Add tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© OpenStreetMap contributors'
    }).addTo(mapInstanceRef.current)

    // Add click handler
    mapInstanceRef.current.on('click', (e) => {
      const { lat, lng } = e.latlng
      onMapClick(lat, lng)
    })

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove()
      }
    }
  }, [onMapClick])

  useEffect(() => {
    if (!mapInstanceRef.current || !data.length) return

    // Clear existing markers
    markersRef.current.forEach(marker => {
      mapInstanceRef.current.removeLayer(marker)
    })
    markersRef.current = []

    // Add risk visualization (sample of data points to avoid performance issues)
    const sampleData = data.filter((_, index) => index % 10 === 0) // Show every 10th point

    sampleData.forEach(point => {
      const riskValue = point.risks.historical?.ssp126?.all || 0
      const color = getRiskColor(riskValue)
      
      const circle = L.circleMarker([point.lat, point.lng], {
        radius: 4,
        fillColor: color,
        color: color,
        weight: 1,
        opacity: 0.8,
        fillOpacity: 0.6
      })

      circle.bindPopup(`
        <div class="p-2">
          <h4 class="font-semibold">Grid ${point.gridId}</h4>
          <p class="text-sm"><strong>Location:</strong> ${point.state}, ${point.district}</p>
          <p class="text-sm"><strong>Coordinates:</strong> ${point.lat.toFixed(3)}, ${point.lng.toFixed(3)}</p>
          <p class="text-sm"><strong>Overall Risk:</strong> ${(riskValue * 100).toFixed(1)}%</p>
        </div>
      `)

      circle.addTo(mapInstanceRef.current)
      markersRef.current.push(circle)
    })
  }, [data])

  useEffect(() => {
    if (!mapInstanceRef.current || !selectedLocation) return

    // Add selected location marker
    const marker = L.marker([selectedLocation.searchLat || selectedLocation.lat, selectedLocation.searchLng || selectedLocation.lng])
      .addTo(mapInstanceRef.current)
      .bindPopup(`
        <div class="p-2">
          <h4 class="font-semibold">Selected Location</h4>
          <p class="text-sm"><strong>Nearest Grid:</strong> ${selectedLocation.gridId}</p>
          <p class="text-sm"><strong>Distance:</strong> ${(selectedLocation.distance * 111).toFixed(2)} km</p>
        </div>
      `)
      .openPopup()

    // Pan to selected location
    mapInstanceRef.current.setView([selectedLocation.searchLat || selectedLocation.lat, selectedLocation.searchLng || selectedLocation.lng], 8)

    return () => {
      mapInstanceRef.current.removeLayer(marker)
    }
  }, [selectedLocation])

  const getRiskColor = (riskValue) => {
    if (riskValue >= 0.25) return '#dc2626' // High risk - red
    if (riskValue >= 0.2) return '#f59e0b'  // Medium risk - yellow
    if (riskValue >= 0.15) return '#10b981' // Low risk - green
    return '#6b7280' // Very low risk - gray
  }

  return (
    <div className="relative">
      <div 
        ref={mapRef} 
        className="w-full h-96 rounded-lg border border-gray-200"
        style={{ minHeight: '400px' }}
      />
      
      {/* Legend */}
      <div className="absolute bottom-4 left-4 bg-white p-3 rounded-lg shadow-lg border border-gray-200">
        <h4 className="text-sm font-semibold text-gray-900 mb-2">Risk Level</h4>
        <div className="space-y-1">
          <div className="flex items-center text-xs">
            <div className="w-3 h-3 rounded-full bg-red-600 mr-2"></div>
            <span>High (≥25%)</span>
          </div>
          <div className="flex items-center text-xs">
            <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
            <span>Medium (20-25%)</span>
          </div>
          <div className="flex items-center text-xs">
            <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
            <span>Low (15-20%)</span>
          </div>
          <div className="flex items-center text-xs">
            <div className="w-3 h-3 rounded-full bg-gray-500 mr-2"></div>
            <span>Very Low (&lt;15%)</span>
          </div>
        </div>
      </div>

      {/* Instructions */}
      <div className="absolute top-4 right-4 bg-white p-3 rounded-lg shadow-lg border border-gray-200 max-w-xs">
        <p className="text-xs text-gray-600">
          Click anywhere on the map to analyze climate risks for that location. 
          Colored dots represent risk levels at different grid points.
        </p>
      </div>
    </div>
  )
}

export default ClimateMap
