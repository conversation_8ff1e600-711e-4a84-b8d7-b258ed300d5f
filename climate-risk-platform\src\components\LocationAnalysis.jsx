import { useState } from 'react'
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts'
import { MapPin, TrendingUp, AlertTriangle } from 'lucide-react'

const LocationAnalysis = ({ location }) => {
  const [activeTab, setActiveTab] = useState('overview')

  const tabs = [
    { id: 'overview', label: 'Overview' },
    { id: 'temporal', label: 'Temporal Analysis' },
    { id: 'breakdown', label: 'Risk Breakdown' }
  ]

  const getRiskLevel = (value) => {
    if (value >= 0.25) return { level: 'High', color: 'text-red-600 bg-red-50' }
    if (value >= 0.2) return { level: 'Medium', color: 'text-yellow-600 bg-yellow-50' }
    if (value >= 0.15) return { level: 'Low', color: 'text-green-600 bg-green-50' }
    return { level: 'Very Low', color: 'text-gray-600 bg-gray-50' }
  }

  const formatRiskValue = (value) => {
    return (value * 100).toFixed(1) + '%'
  }

  // Prepare data for charts
  const historicalRisk = location.risks.historical?.ssp126 || {}
  const longTermRisk = location.risks.longTerm?.ssp126 || {}

  const riskComparison = [
    { name: 'Historical', value: (historicalRisk.all || 0) * 100 },
    { name: 'Long-term', value: (longTermRisk.all || 0) * 100 }
  ]

  const riskBreakdown = [
    { name: 'Flood', historical: (historicalRisk.flood || 0) * 100, longTerm: (longTermRisk.flood || 0) * 100, color: '#3b82f6' },
    { name: 'Drought', historical: (historicalRisk.drought || 0) * 100, longTerm: (longTermRisk.drought || 0) * 100, color: '#f59e0b' },
    { name: 'Heatwave', historical: (historicalRisk.heatwave || 0) * 100, longTerm: (longTermRisk.heatwave || 0) * 100, color: '#ef4444' },
    { name: 'SLR', historical: (historicalRisk.slr || 0) * 100, longTerm: (longTermRisk.slr || 0) * 100, color: '#06b6d4' },
    { name: 'Wildfire', historical: (historicalRisk.wildfire || 0) * 100, longTerm: (longTermRisk.wildfire || 0) * 100, color: '#f97316' },
    { name: 'Cyclone', historical: (historicalRisk.cyclone || 0) * 100, longTerm: (longTermRisk.cyclone || 0) * 100, color: '#8b5cf6' }
  ]

  const temporalData = [
    { period: 'Historical', risk: (location.risks.historical?.ssp126?.all || 0) * 100 },
    { period: 'Near-term', risk: (location.risks.nearTerm?.ssp126?.all || 0) * 100 },
    { period: 'Mid-term', risk: (location.risks.midTerm?.ssp126?.all || 0) * 100 },
    { period: 'Long-term', risk: (location.risks.longTerm?.ssp126?.all || 0) * 100 }
  ]

  const pieData = riskBreakdown.map(item => ({
    name: item.name,
    value: item.historical,
    color: item.color
  }))

  const overallRisk = getRiskLevel(historicalRisk.all || 0)
  const longTermOverallRisk = getRiskLevel(longTermRisk.all || 0)

  return (
    <div className="space-y-6">
      {/* Location Info */}
      <div className="border-b border-gray-200 pb-4">
        <div className="flex items-center mb-2">
          <MapPin className="h-5 w-5 text-gray-600 mr-2" />
          <h3 className="text-lg font-semibold text-gray-900">
            Grid {location.gridId}
          </h3>
        </div>
        <div className="text-sm text-gray-600 space-y-1">
          <p><strong>State:</strong> {location.state}</p>
          <p><strong>District:</strong> {location.district}</p>
          <p><strong>Tehsil:</strong> {location.tehsil}</p>
          <p><strong>Coordinates:</strong> {location.lat.toFixed(4)}, {location.lng.toFixed(4)}</p>
          {location.distance && (
            <p><strong>Distance from search:</strong> {(location.distance * 111).toFixed(2)} km</p>
          )}
        </div>
      </div>

      {/* Risk Summary */}
      <div className="grid grid-cols-2 gap-4">
        <div className="text-center p-4 bg-gray-50 rounded-lg">
          <div className="text-sm text-gray-600 mb-1">Historical Risk</div>
          <div className={`text-lg font-semibold px-3 py-1 rounded-full ${overallRisk.color}`}>
            {formatRiskValue(historicalRisk.all || 0)}
          </div>
        </div>
        <div className="text-center p-4 bg-gray-50 rounded-lg">
          <div className="text-sm text-gray-600 mb-1">Long-term Risk</div>
          <div className={`text-lg font-semibold px-3 py-1 rounded-full ${longTermOverallRisk.color}`}>
            {formatRiskValue(longTermRisk.all || 0)}
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-4">
        {activeTab === 'overview' && (
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">Historical vs Long-term Risk Comparison</h4>
            <ResponsiveContainer width="100%" height={200}>
              <BarChart data={riskComparison}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" fontSize={12} />
                <YAxis label={{ value: 'Risk (%)', angle: -90, position: 'insideLeft' }} fontSize={12} />
                <Tooltip formatter={(value) => [`${value.toFixed(1)}%`, 'Risk Level']} />
                <Bar dataKey="value" fill="#3b82f6" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        )}

        {activeTab === 'temporal' && (
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">Risk Projection Over Time</h4>
            <ResponsiveContainer width="100%" height={200}>
              <LineChart data={temporalData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="period" fontSize={12} />
                <YAxis label={{ value: 'Risk (%)', angle: -90, position: 'insideLeft' }} fontSize={12} />
                <Tooltip formatter={(value) => [`${value.toFixed(1)}%`, 'Risk Level']} />
                <Line 
                  type="monotone" 
                  dataKey="risk" 
                  stroke="#ef4444" 
                  strokeWidth={2}
                  dot={{ fill: '#ef4444', strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>
            
            <div className="mt-4">
              <h5 className="font-medium text-gray-900 mb-2">Risk Trend Analysis</h5>
              <div className="text-sm text-gray-600">
                {temporalData[3].risk > temporalData[0].risk ? (
                  <div className="flex items-center text-red-600">
                    <TrendingUp className="h-4 w-4 mr-1" />
                    Risk is projected to increase by {(temporalData[3].risk - temporalData[0].risk).toFixed(1)}% by 2080s
                  </div>
                ) : (
                  <div className="flex items-center text-green-600">
                    <TrendingUp className="h-4 w-4 mr-1 transform rotate-180" />
                    Risk is projected to decrease by {(temporalData[0].risk - temporalData[3].risk).toFixed(1)}% by 2080s
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'breakdown' && (
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">Risk Type Breakdown</h4>
            
            {/* Historical Risk Pie Chart */}
            <div>
              <h5 className="text-sm font-medium text-gray-700 mb-2">Historical Risk Distribution</h5>
              <ResponsiveContainer width="100%" height={200}>
                <PieChart>
                  <Pie
                    data={pieData}
                    cx="50%"
                    cy="50%"
                    outerRadius={60}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, value }) => `${name}: ${value.toFixed(1)}%`}
                    labelLine={false}
                  >
                    {pieData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`${value.toFixed(1)}%`, 'Risk Level']} />
                </PieChart>
              </ResponsiveContainer>
            </div>

            {/* Risk Type Comparison */}
            <div>
              <h5 className="text-sm font-medium text-gray-700 mb-2">Historical vs Long-term by Risk Type</h5>
              <ResponsiveContainer width="100%" height={200}>
                <BarChart data={riskBreakdown}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" fontSize={12} />
                  <YAxis label={{ value: 'Risk (%)', angle: -90, position: 'insideLeft' }} fontSize={12} />
                  <Tooltip formatter={(value) => [`${value.toFixed(1)}%`, 'Risk Level']} />
                  <Bar dataKey="historical" fill="#3b82f6" name="Historical" />
                  <Bar dataKey="longTerm" fill="#ef4444" name="Long-term" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default LocationAnalysis
