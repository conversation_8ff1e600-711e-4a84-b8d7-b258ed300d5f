import { Link } from 'react-router-dom'
import { BarChart3, Map, TrendingUp, Thermometer, Droplets, Wind } from 'lucide-react'

const Home = () => {
  const scenarios = [
    {
      name: 'SSP1-2.6',
      description: 'Sustainability - Taking the Green Road',
      emissions: 'Low',
      warming: '~1.5-2.0°C by 2100',
      color: 'bg-green-100 text-green-800'
    },
    {
      name: 'SSP2-4.5',
      description: 'Middle of the Road',
      emissions: 'Medium-Low',
      warming: '~2.5-3.0°C by 2100',
      color: 'bg-yellow-100 text-yellow-800'
    },
    {
      name: 'SSP3-7.0',
      description: 'Regional Rivalry - A Rocky Road',
      emissions: 'Medium-High',
      warming: '~3.5-4.0°C by 2100',
      color: 'bg-orange-100 text-orange-800'
    },
    {
      name: 'SSP5-8.5',
      description: 'Fossil-fueled Development - Taking the Highway',
      emissions: 'High',
      warming: '~4.5-5.0°C by 2100',
      color: 'bg-red-100 text-red-800'
    }
  ]

  const features = [
    {
      icon: BarChart3,
      title: 'Data Visualization',
      description: 'Interactive maps and charts to visualize climate risk data across regions.',
      color: 'text-blue-600'
    },
    {
      icon: TrendingUp,
      title: 'Risk Analysis',
      description: 'Analyze different types of climate risks including floods, droughts, heatwaves, and more.',
      color: 'text-green-600'
    },
    {
      icon: Thermometer,
      title: 'Future Scenarios',
      description: 'Explore different climate scenarios (SSP126, SSP245, SSP370, SSP585) and time periods.',
      color: 'text-red-600'
    }
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-primary-600 to-primary-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Climate Risk Analysis Platform
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-primary-100 max-w-3xl mx-auto">
              Analyze and visualize climate risk data across different scenarios and time periods.
            </p>
            <p className="text-lg mb-12 text-primary-200">
              Your data is already available—explore insights via our interactive dashboard.
            </p>
            <Link
              to="/dashboard"
              className="inline-flex items-center px-8 py-4 bg-white text-primary-600 font-semibold rounded-lg hover:bg-gray-50 transition-colors duration-200 shadow-lg"
            >
              <BarChart3 className="h-5 w-5 mr-2" />
              View Dashboard
            </Link>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center">
                <div className="flex justify-center mb-4">
                  <feature.icon className={`h-12 w-12 ${feature.color}`} />
                </div>
                <h3 className="text-xl font-semibold mb-4 text-gray-900">
                  {feature.title}
                </h3>
                <p className="text-gray-600">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Climate Scenarios Section */}
      <div className="py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              About Climate Scenarios
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Explore different climate scenarios to understand potential future climate risks
            </p>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full bg-white rounded-lg shadow-sm">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Scenario</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Description</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Emissions</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Warming</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {scenarios.map((scenario, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <span className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${scenario.color}`}>
                        {scenario.name}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">{scenario.description}</td>
                    <td className="px-6 py-4 text-sm text-gray-600">{scenario.emissions}</td>
                    <td className="px-6 py-4 text-sm text-gray-600">{scenario.warming}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-16 bg-primary-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to Explore Climate Risks?
          </h2>
          <p className="text-xl text-primary-100 mb-8">
            Start analyzing climate data with our interactive tools
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/dashboard"
              className="inline-flex items-center px-6 py-3 bg-white text-primary-600 font-semibold rounded-lg hover:bg-gray-50 transition-colors duration-200"
            >
              <BarChart3 className="h-5 w-5 mr-2" />
              Dashboard
            </Link>
            <Link
              to="/map-analysis"
              className="inline-flex items-center px-6 py-3 bg-primary-700 text-white font-semibold rounded-lg hover:bg-primary-800 transition-colors duration-200 border border-primary-500"
            >
              <Map className="h-5 w-5 mr-2" />
              Map Analysis
            </Link>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p>&copy; 2025 Climate Risk Platform. All rights reserved.</p>
        </div>
      </footer>
    </div>
  )
}

export default Home
