import { useState, useEffect } from 'react'
import { Search, MapPin, Info } from 'lucide-react'
import ClimateMap from '../components/ClimateMap'
import LocationAnalysis from '../components/LocationAnalysis'
import LoadingSpinner from '../components/LoadingSpinner'

const MapAnalysis = () => {
  const [data, setData] = useState([])
  const [loading, setLoading] = useState(true)
  const [selectedLocation, setSelectedLocation] = useState(null)
  const [coordinates, setCoordinates] = useState({ lat: '', lng: '' })
  const [searchLoading, setSearchLoading] = useState(false)

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      // Load sample data for development
      const response = await fetch('/data/sample-data.json')
      const jsonData = await response.json()
      setData(jsonData)
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleMapClick = (lat, lng) => {
    findNearestGrid(lat, lng)
  }

  const handleCoordinateSearch = () => {
    const lat = parseFloat(coordinates.lat)
    const lng = parseFloat(coordinates.lng)
    
    if (isNaN(lat) || isNaN(lng)) {
      alert('Please enter valid coordinates')
      return
    }
    
    if (lat < 8 || lat > 37 || lng < 68 || lng > 98) {
      alert('Coordinates must be within India (Lat: 8-37, Lng: 68-98)')
      return
    }
    
    findNearestGrid(lat, lng)
  }

  const findNearestGrid = (lat, lng) => {
    setSearchLoading(true)
    
    // Find the nearest grid point
    let nearest = null
    let minDistance = Infinity
    
    data.forEach(point => {
      const distance = Math.sqrt(
        Math.pow(point.lat - lat, 2) + Math.pow(point.lng - lng, 2)
      )
      if (distance < minDistance) {
        minDistance = distance
        nearest = point
      }
    })
    
    if (nearest) {
      setSelectedLocation({
        ...nearest,
        searchLat: lat,
        searchLng: lng,
        distance: minDistance
      })
    }
    
    setSearchLoading(false)
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Map-Based Climate Risk Analysis
          </h1>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start">
              <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
              <div>
                <h3 className="text-sm font-medium text-blue-800 mb-1">How to use:</h3>
                <p className="text-sm text-blue-700">
                  Click anywhere on the map to analyze climate risks for that location, or use the coordinate search box to enter specific latitude and longitude values. The system will find the corresponding grid and display detailed climate risk data with visualizations.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Coordinate Search */}
        <div className="mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              Search by Coordinates
            </h2>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Latitude
                </label>
                <input
                  type="number"
                  step="0.001"
                  placeholder="e.g., 28.6139"
                  value={coordinates.lat}
                  onChange={(e) => setCoordinates(prev => ({ ...prev, lat: e.target.value }))}
                  className="input-field"
                />
              </div>
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Longitude
                </label>
                <input
                  type="number"
                  step="0.001"
                  placeholder="e.g., 77.2090"
                  value={coordinates.lng}
                  onChange={(e) => setCoordinates(prev => ({ ...prev, lng: e.target.value }))}
                  className="input-field"
                />
              </div>
              <div className="flex items-end">
                <button
                  onClick={handleCoordinateSearch}
                  disabled={searchLoading}
                  className="btn-primary flex items-center"
                >
                  {searchLoading ? (
                    <div className="loading-spinner mr-2" />
                  ) : (
                    <Search className="h-4 w-4 mr-2" />
                  )}
                  Analyze Location
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Map and Analysis */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Map */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              Interactive Map
            </h2>
            <ClimateMap
              data={data}
              onMapClick={handleMapClick}
              selectedLocation={selectedLocation}
            />
          </div>

          {/* Analysis Panel */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              Climate Risk Analysis
            </h2>
            {selectedLocation ? (
              <LocationAnalysis location={selectedLocation} />
            ) : (
              <div className="text-center py-12">
                <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">
                  Click on the map or search by coordinates to analyze climate risks for a specific location.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default MapAnalysis
