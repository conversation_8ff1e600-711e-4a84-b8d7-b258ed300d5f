import pandas as pd
import json
import os

# Read the Excel file
excel_path = '../../Clim_idx_5km_v2.xlsx'
df = pd.read_excel(excel_path)

print(f"Processing {len(df)} records...")

# Create data directory
os.makedirs('../public/data', exist_ok=True)

# Convert to JSON with optimized structure
data = []
for _, row in df.iterrows():
    # Handle NaN values
    state = str(row['STATE']) if pd.notna(row['STATE']) else 'Unknown'
    district = str(row['District']) if pd.notna(row['District']) else 'Unknown'
    tehsil = str(row['TEHSIL']) if pd.notna(row['TEHSIL']) else 'Unknown'

    record = {
        'gridId': int(row['Grid_ID']),
        'lat': float(row['Latitude']),
        'lng': float(row['Longitude']),
        'state': state,
        'district': district,
        'tehsil': tehsil,
        'risks': {
            'historical': {
                'ssp126': {
                    'all': float(row['Hist_SSP126_All']),
                    'flood': float(row['Hist_SSP126_Flood']),
                    'drought': float(row['Hist_SSP126_Drought']),
                    'heatwave': float(row['Hist_SSP126_Heatwave']),
                    'slr': float(row['Hist_SSP126_SLR']),
                    'wildfire': float(row['Hist_SSP126_Wildfire']),
                    'cyclone': float(row['Hist_SSP126_Cyclone'])
                },
                'ssp245': {
                    'all': float(row['Hist_SSP245_All']),
                    'flood': float(row['Hist_SSP245_Flood']),
                    'drought': float(row['Hist_SSP245_Drought']),
                    'heatwave': float(row['Hist_SSP245_Heatwave']),
                    'slr': float(row['Hist_SSP245_SLR']),
                    'wildfire': float(row['Hist_SSP245_Wildfire']),
                    'cyclone': float(row['Hist_SSP245_Cyclone'])
                }
            },
            'nearTerm': {
                'ssp126': {
                    'all': float(row['Near_SSP126_All']),
                    'flood': float(row['Near_SSP126_Flood']),
                    'drought': float(row['Near_SSP126_Drought']),
                    'heatwave': float(row['Near_SSP126_Heatwave']),
                    'slr': float(row['Near_SSP126_SLR']),
                    'wildfire': float(row['Near_SSP126_Wildfire']),
                    'cyclone': float(row['Near_SSP126_Cyclone'])
                }
            },
            'midTerm': {
                'ssp126': {
                    'all': float(row['Mid_SSP126_All']),
                    'flood': float(row['Mid_SSP126_Flood']),
                    'drought': float(row['Mid_SSP126_Drought']),
                    'heatwave': float(row['Mid_SSP126_Heatwave']),
                    'slr': float(row['Mid_SSP126_SLR']),
                    'wildfire': float(row['Mid_SSP126_Wildfire']),
                    'cyclone': float(row['Mid_SSP126_Cyclone'])
                }
            },
            'longTerm': {
                'ssp126': {
                    'all': float(row['Long_SSP126_All']),
                    'flood': float(row['Long_SSP126_Flood']),
                    'drought': float(row['Long_SSP126_Drought']),
                    'heatwave': float(row['Long_SSP126_Heatwave']),
                    'slr': float(row['Long_SSP126_SLR']),
                    'wildfire': float(row['Long_SSP126_Wildfire']),
                    'cyclone': float(row['Long_SSP126_Cyclone'])
                }
            }
        }
    }
    data.append(record)

# Save full dataset (this will be large)
print("Saving full dataset...")
with open('../public/data/climate-data.json', 'w') as f:
    json.dump(data, f, separators=(',', ':'))

# Create summary data for quick loading
print("Creating summary data...")
states = [str(x) for x in df['STATE'].unique() if pd.notna(x)]
districts = [str(x) for x in df['District'].unique() if pd.notna(x)]
tehsils = [str(x) for x in df['TEHSIL'].unique() if pd.notna(x)]

summary = {
    'totalRecords': len(data),
    'states': sorted(states),
    'districts': sorted(districts),
    'tehsils': sorted(tehsils),
    'riskTypes': ['flood', 'drought', 'heatwave', 'slr', 'wildfire', 'cyclone'],
    'scenarios': ['ssp126', 'ssp245', 'ssp370', 'ssp585'],
    'timePeriods': ['historical', 'nearTerm', 'midTerm', 'longTerm']
}

with open('../public/data/summary.json', 'w') as f:
    json.dump(summary, f, indent=2)

# Create sample data for development (first 1000 records)
print("Creating sample data...")
sample_data = data[:1000]
with open('../public/data/sample-data.json', 'w') as f:
    json.dump(sample_data, f, separators=(',', ':'))

print("Data conversion complete!")
print(f"- Full dataset: {len(data)} records")
print(f"- Sample dataset: {len(sample_data)} records")
print(f"- States: {len(states)}")
print(f"- Districts: {len(districts)}")
print(f"- Tehsils: {len(tehsils)}")
