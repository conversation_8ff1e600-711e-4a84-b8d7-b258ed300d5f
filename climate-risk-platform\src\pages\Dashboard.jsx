import { useState, useEffect } from 'react'
import { Filter, Download, RefreshCw } from 'lucide-react'
import FilterPanel from '../components/FilterPanel'
import DataTable from '../components/DataTable'
import RiskCharts from '../components/RiskCharts'
import LoadingSpinner from '../components/LoadingSpinner'

const Dashboard = () => {
  const [data, setData] = useState([])
  const [filteredData, setFilteredData] = useState([])
  const [loading, setLoading] = useState(true)
  const [filters, setFilters] = useState({
    state: '',
    district: '',
    tehsil: '',
    scenario: 'ssp126',
    timePeriod: 'historical'
  })
  const [summary, setSummary] = useState(null)

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      // Load sample data for development
      const response = await fetch('/data/sample-data.json')
      const jsonData = await response.json()
      setData(jsonData)
      setFilteredData(jsonData)

      // Load summary
      const summaryResponse = await fetch('/data/summary.json')
      const summaryData = await summaryResponse.json()
      setSummary(summaryData)
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setLoading(false)
    }
  }

  const applyFilters = () => {
    let filtered = data

    if (filters.state) {
      filtered = filtered.filter(item => item.state === filters.state)
    }
    if (filters.district) {
      filtered = filtered.filter(item => item.district === filters.district)
    }
    if (filters.tehsil) {
      filtered = filtered.filter(item => item.tehsil === filters.tehsil)
    }

    setFilteredData(filtered)
  }

  const clearFilters = () => {
    setFilters({
      state: '',
      district: '',
      tehsil: '',
      scenario: 'ssp126',
      timePeriod: 'historical'
    })
    setFilteredData(data)
  }

  const exportData = () => {
    const csv = convertToCSV(filteredData)
    downloadCSV(csv, 'climate-risk-data.csv')
  }

  const convertToCSV = (data) => {
    if (!data.length) return ''
    
    const headers = ['Grid ID', 'State', 'District', 'Tehsil', 'Latitude', 'Longitude', 'Overall Risk', 'Flood Risk', 'Drought Risk', 'Heatwave Risk']
    const rows = data.map(item => [
      item.gridId,
      item.state,
      item.district,
      item.tehsil,
      item.lat,
      item.lng,
      item.risks[filters.timePeriod]?.[filters.scenario]?.all || 0,
      item.risks[filters.timePeriod]?.[filters.scenario]?.flood || 0,
      item.risks[filters.timePeriod]?.[filters.scenario]?.drought || 0,
      item.risks[filters.timePeriod]?.[filters.scenario]?.heatwave || 0
    ])
    
    return [headers, ...rows].map(row => row.join(',')).join('\n')
  }

  const downloadCSV = (csv, filename) => {
    const blob = new Blob([csv], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    a.click()
    window.URL.revokeObjectURL(url)
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Climate Risk Dashboard
          </h1>
          <p className="text-gray-600">
            Analyze climate risk data with advanced filtering and visualization tools
          </p>
        </div>

        {/* Filter Panel */}
        <div className="mb-8">
          <FilterPanel
            filters={filters}
            setFilters={setFilters}
            onApply={applyFilters}
            onClear={clearFilters}
            summary={summary}
          />
        </div>

        {/* Action Bar */}
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-600">
              Showing {filteredData.length} records
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={loadData}
              className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </button>
            <button
              onClick={exportData}
              className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </button>
          </div>
        </div>

        {/* Charts Section */}
        <div className="mb-8">
          <RiskCharts 
            data={filteredData} 
            scenario={filters.scenario}
            timePeriod={filters.timePeriod}
          />
        </div>

        {/* Data Table */}
        <div className="bg-white rounded-lg shadow-sm">
          <DataTable 
            data={filteredData}
            scenario={filters.scenario}
            timePeriod={filters.timePeriod}
          />
        </div>
      </div>
    </div>
  )
}

export default Dashboard
