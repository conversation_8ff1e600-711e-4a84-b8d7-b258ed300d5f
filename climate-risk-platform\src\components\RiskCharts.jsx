import { useMemo } from 'react'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  ResponsiveContainer
} from 'recharts'

const RiskCharts = ({ data, scenario, timePeriod }) => {
  const chartData = useMemo(() => {
    if (!data.length) return { riskDistribution: [], riskComparison: [], temporalAnalysis: [] }

    // Calculate risk distribution
    const riskTypes = ['flood', 'drought', 'heatwave', 'slr', 'wildfire', 'cyclone']
    const riskDistribution = riskTypes.map(riskType => {
      const values = data.map(item => item.risks[timePeriod]?.[scenario]?.[riskType] || 0)
      const average = values.reduce((sum, val) => sum + val, 0) / values.length
      return {
        name: riskType.charAt(0).toUpperCase() + riskType.slice(1),
        value: average * 100,
        color: getRiskColor(riskType)
      }
    })

    // Calculate risk level distribution
    const riskLevels = { 'Very Low': 0, 'Low': 0, 'Medium': 0, 'High': 0 }
    data.forEach(item => {
      const overallRisk = item.risks[timePeriod]?.[scenario]?.all || 0
      const level = getRiskLevel(overallRisk)
      riskLevels[level]++
    })

    const riskComparison = Object.entries(riskLevels).map(([level, count]) => ({
      name: level,
      count,
      percentage: ((count / data.length) * 100).toFixed(1)
    }))

    // Temporal analysis (if we have multiple time periods)
    const timePeriods = ['historical', 'nearTerm', 'midTerm', 'longTerm']
    const temporalAnalysis = timePeriods.map(period => {
      const values = data.map(item => item.risks[period]?.[scenario]?.all || 0)
      const average = values.reduce((sum, val) => sum + val, 0) / values.length
      return {
        period: period.charAt(0).toUpperCase() + period.slice(1),
        risk: average * 100
      }
    })

    return { riskDistribution, riskComparison, temporalAnalysis }
  }, [data, scenario, timePeriod])

  const getRiskColor = (riskType) => {
    const colors = {
      flood: '#3b82f6',
      drought: '#f59e0b',
      heatwave: '#ef4444',
      slr: '#06b6d4',
      wildfire: '#f97316',
      cyclone: '#8b5cf6'
    }
    return colors[riskType] || '#6b7280'
  }

  const getRiskLevel = (value) => {
    if (value >= 0.25) return 'High'
    if (value >= 0.2) return 'Medium'
    if (value >= 0.15) return 'Low'
    return 'Very Low'
  }

  const COLORS = ['#10b981', '#f59e0b', '#ef4444', '#dc2626']

  if (!data.length) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow-sm p-6">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div className="h-48 bg-gray-200 rounded"></div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Risk Type Distribution */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Risk Type Distribution
        </h3>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={chartData.riskDistribution}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="name" 
              angle={-45}
              textAnchor="end"
              height={80}
              fontSize={12}
            />
            <YAxis 
              label={{ value: 'Risk Level (%)', angle: -90, position: 'insideLeft' }}
              fontSize={12}
            />
            <Tooltip 
              formatter={(value) => [`${value.toFixed(2)}%`, 'Risk Level']}
            />
            <Bar dataKey="value" fill="#3b82f6" />
          </BarChart>
        </ResponsiveContainer>
      </div>

      {/* Risk Level Distribution */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Risk Level Distribution
        </h3>
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={chartData.riskComparison}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percentage }) => `${name}: ${percentage}%`}
              outerRadius={80}
              fill="#8884d8"
              dataKey="count"
            >
              {chartData.riskComparison.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip formatter={(value, name) => [value, 'Count']} />
          </PieChart>
        </ResponsiveContainer>
      </div>

      {/* Temporal Analysis */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Risk Projection Over Time
        </h3>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={chartData.temporalAnalysis}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="period" 
              fontSize={12}
            />
            <YAxis 
              label={{ value: 'Risk Level (%)', angle: -90, position: 'insideLeft' }}
              fontSize={12}
            />
            <Tooltip 
              formatter={(value) => [`${value.toFixed(2)}%`, 'Average Risk']}
            />
            <Line 
              type="monotone" 
              dataKey="risk" 
              stroke="#ef4444" 
              strokeWidth={3}
              dot={{ fill: '#ef4444', strokeWidth: 2, r: 6 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>

      {/* Summary Statistics */}
      <div className="lg:col-span-3 bg-white rounded-lg shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Summary Statistics
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary-600">
              {data.length}
            </div>
            <div className="text-sm text-gray-600">Total Records</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {chartData.riskComparison.find(r => r.name === 'Low')?.count || 0}
            </div>
            <div className="text-sm text-gray-600">Low Risk Areas</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {chartData.riskComparison.find(r => r.name === 'Medium')?.count || 0}
            </div>
            <div className="text-sm text-gray-600">Medium Risk Areas</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {chartData.riskComparison.find(r => r.name === 'High')?.count || 0}
            </div>
            <div className="text-sm text-gray-600">High Risk Areas</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default RiskCharts
